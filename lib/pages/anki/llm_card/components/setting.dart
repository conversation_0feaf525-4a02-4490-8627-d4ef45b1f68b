import 'package:flutter/material.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/anki/llm_controller.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

class LLMCardSetting extends GetView<LLMCardPageController> {
  const LLMCardSetting({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: const EdgeInsets.all(16.0),
        child: ShadDialog(
          title: Text('anki.llm_card.advanced_settings'.tr),
          child: Material(
            child: Obx(
              () => Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  spacing: 16,
                  children: [
                    ShadInputWithValidate(
                      key: ValueKey(
                          "temperature-${controller.temperature.value}"),
                      label: 'anki.llm_card.temperature'.tr,
                      placeholder: 'anki.llm_card.input_temperature_placeholder'.tr,
                      initialValue: controller.temperature.value.toString(),
                      onChanged: (value) {
                        controller.temperature.value =
                            double.tryParse(value) ?? 0.7;
                      },
                      onValidate: (value) async {
                        if (value.isEmpty) {
                          return "anki.llm_card.temperature_cannot_empty".tr;
                        }
                        if (double.tryParse(value) == null) {
                          return "anki.llm_card.invalid_number".tr;
                        }
                        if (double.tryParse(value)! < 0 ||
                            double.tryParse(value)! > 2) {
                          return "anki.llm_card.temperature_range_error".tr;
                        }
                        return "";
                      },
                    ),
                    // 最大令牌数
                    ShadInputWithValidate(
                      key: ValueKey("maxTokens-${controller.maxTokens.value}"),
                      label: 'anki.llm_card.max_tokens'.tr,
                      placeholder: 'anki.llm_card.input_max_tokens_placeholder'.tr,
                      initialValue: controller.maxTokens.value.toString(),
                      onChanged: (value) {
                        controller.maxTokens.value =
                            int.tryParse(value) ?? 4000;
                      },
                      onValidate: (value) async {
                        if (value.isEmpty) {
                          return "anki.llm_card.tokens_cannot_empty".tr;
                        }
                        if (int.tryParse(value) == null) {
                          return "anki.llm_card.invalid_number".tr;
                        }
                        return "";
                      },
                    ),
                    // 最大并发请求数
                    ShadInputWithValidate(
                      key: ValueKey(
                          "maxConcurrentRequests-${controller.maxConcurrentRequests.value}"),
                      label: 'anki.llm_card.max_concurrent_requests'.tr,
                      placeholder: 'anki.llm_card.input_max_concurrent_requests_placeholder'.tr,
                      initialValue:
                          controller.maxConcurrentRequests.value.toString(),
                      onChanged: (value) {
                        controller.maxConcurrentRequests.value =
                            int.tryParse(value) ?? 1;
                      },
                      onValidate: (value) async {
                        if (value.isEmpty) {
                          return "anki.llm_card.max_concurrent_requests_cannot_empty".tr;
                        }
                        if (int.tryParse(value) == null) {
                          return "anki.llm_card.invalid_number".tr;
                        }
                        if (int.tryParse(value)! < 1) {
                          return "anki.llm_card.max_concurrent_requests_range_error".tr;
                        }
                        return "";
                      },
                    ),
                    // Top P
                    ShadInputWithValidate(
                      key: ValueKey("topP-${controller.topP.value}"),
                      label: 'anki.llm_card.top_p'.tr,
                      placeholder: 'anki.llm_card.input_top_p_placeholder'.tr,
                      initialValue: controller.topP.value.toString(),
                      onChanged: (value) {
                        controller.topP.value = double.tryParse(value) ?? 0.95;
                      },
                      onValidate: (value) async {
                        if (value.isEmpty) {
                          return "anki.llm_card.top_p_cannot_empty".tr;
                        }
                        if (double.tryParse(value) == null) {
                          return "anki.llm_card.invalid_number".tr;
                        }
                        if (double.tryParse(value)! < 0 ||
                            double.tryParse(value)! > 1) {
                          return "anki.llm_card.top_p_range_error".tr;
                        }
                        return "";
                      },
                    ),
                    // Top K
                    ShadInputWithValidate(
                      key: ValueKey("topK-${controller.topK.value}"),
                      label: 'anki.llm_card.top_k'.tr,
                      placeholder: 'anki.llm_card.input_top_k_placeholder'.tr,
                      initialValue: controller.topK.value.toString(),
                      onChanged: (value) {
                        controller.topK.value = int.tryParse(value) ?? 10;
                      },
                      onValidate: (value) async {
                        if (value.isEmpty) {
                          return "anki.llm_card.top_k_cannot_empty".tr;
                        }
                        if (int.tryParse(value) == null) {
                          return "anki.llm_card.invalid_number".tr;
                        }
                        return "";
                      },
                    ),
                    // 超时
                    ShadInputWithValidate(
                      key: ValueKey("timeout-${controller.timeout.value}"),
                      label: 'anki.llm_card.timeout_seconds'.tr,
                      placeholder: 'anki.llm_card.input_timeout_placeholder'.tr,
                      initialValue: controller.timeout.value.toString(),
                      onChanged: (value) {
                        controller.timeout.value = int.tryParse(value) ?? 10;
                      },
                      onValidate: (value) async {
                        if (value.isEmpty) {
                          return "anki.llm_card.timeout_cannot_empty".tr;
                        }
                        if (int.tryParse(value) == null) {
                          return "anki.llm_card.invalid_number".tr;
                        }
                        return "";
                      },
                    ),
                    // 文本分块大小
                    ShadInputWithValidate(
                      key: ValueKey("chunkSize-${controller.chunkSize.value}"),
                      label: 'anki.llm_card.chunk_size'.tr,
                      placeholder: 'anki.llm_card.input_chunk_size_placeholder'.tr,
                      initialValue: controller.chunkSize.value.toString(),
                      onChanged: (value) {
                        controller.chunkSize.value =
                            int.tryParse(value) ?? 3000;
                      },
                      onValidate: (value) async {
                        if (value.isEmpty) {
                          return "anki.llm_card.chunk_size_cannot_empty".tr;
                        }
                        if (int.tryParse(value) == null) {
                          return "anki.llm_card.invalid_number".tr;
                        }
                        if (int.tryParse(value)! < 1) {
                          return "anki.llm_card.chunk_size_range_error".tr;
                        }
                        return "";
                      },
                    ),
                    ShadRadioGroupCustom(
                      key: ValueKey(
                          'prompt-cloze-mode-${controller.clozeMode.value}'),
                      label: 'anki.llm_card.default_cloze_mode'.tr,
                      initialValue: controller.clozeMode.value,
                      items: controller.clozeModeList,
                      onChanged: (value) {
                        controller.clozeMode.value = value;
                      },
                    ),
                    ShadSwitchCustom(
                      key: ValueKey(
                          'prompt-is-per-cloze-per-card-${controller.isPerClozePerCard.value}'),
                      label: 'anki.llm_card.one_cloze_per_card'.tr,
                      initialValue: controller.isPerClozePerCard.value,
                      onChanged: (value) {
                        controller.isPerClozePerCard.value = value;
                      },
                    ),
                    ShadSwitchCustom(
                      key: ValueKey(
                          'prompt-is-html-escape-${controller.isHtmlEscape.value}'),
                      label: 'anki.llm_card.html_escape'.tr,
                      initialValue: controller.isHtmlEscape.value,
                      onChanged: (value) {
                        controller.isHtmlEscape.value = value;
                      },
                    ),
                    ShadInputWithFileSelect(
                      key: ValueKey("log-dir-${controller.logdir.value}"),
                      title: 'anki.llm_card.log_directory'.tr,
                      placeholder: Text('anki.llm_card.log_directory'.tr),
                      initialValue: [controller.logdir.value],
                      isRequired: true,
                      isFolder: true,
                      onFilesSelected: (value) {
                        controller.logdir.value = value.single;
                        controller.saveSettings();
                      },
                      onValidate: (value, files) async {
                        return await validateOutputDir(value, files);
                      },
                      onValidateError: (error) {},
                    ),
                  ],
                ),
              ),
            ),
          ),
        ));
  }
}
